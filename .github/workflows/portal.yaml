name: portal

on:
  pull_request:
    branches:
      - master
      - "release/portal-*"
      - "ci/portal/**"
    paths:
      - "golang/go.*"
      - "golang/pkg/**"
      - "golang/services/portal/**"
      - "pnpm-lock.yaml"
      - "protos"
      - "ui/common/**"
  push:
    branches:
      - master
      - "release/portal-*"
    paths:
      - "golang/go.*"
      - "golang/pkg/**"
      - "golang/services/portal/**"
      - "pnpm-lock.yaml"
      - "protos"
      - "ui/common/**"
    tags:
      - portal-v*.*.*

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/portal
  DOCKERFILE: golang/services/portal/Dockerfile
  PROJECT_TITLE: carbon portal backend
  PROJECT_DESCRIPTION: api, proxy, and static files for portal

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Register built on date
        run: echo "BUILT_ON=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}
          lfs: true

      - name: Verify go.mod and go.sum match submodule hash
        run: |
            PROTOS_HASH=$(cd protos && git rev-parse --short=12 HEAD) 
  
            if ! (grep -q "$PROTOS_HASH" golang/go.mod && grep -q "$PROTOS_HASH" golang/go.sum); then
              echo "::error::ERROR: go.mod or go.sum does not reference the correct protos submodule commit ($PROTOS_HASH)"
              exit 1
            fi

            echo "go.mod and go.sum are correctly synced with protos submodule."

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          labels: |
            org.opencontainers.image.title=${{ env.PROJECT_TITLE }}
            org.opencontainers.image.description=${{ env.PROJECT_DESCRIPTION }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=ref,event=tag
            type=semver,pattern={{raw}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          secrets: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          build-args: |
            VERSION=${{github.ref_name}}
            COMMIT=${{github.sha}}
            BUILT_ON=${{ env.BUILT_ON }}
            SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
          context: ./
          file: ${{ env.DOCKERFILE }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Update portal staging image on release
        if: startsWith(github.ref, 'refs/tags')
        uses: clowdhaus/argo-cd-action/@v1.9.0
        with:
          command: app set staging --kustomize-image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{github.ref_name}}
          options: --server argocd.cloud.carbonrobotics.com --auth-token ${{secrets.ARGOCD_STAGE_TOKEN}}
