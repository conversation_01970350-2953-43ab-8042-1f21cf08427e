name: test-golang

on:
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/test-golang.yaml'
      - 'golang/go.*'
      - '**.go'
  push:
    branches:
      - master
    tags:
      - .*
    paths:
      - '.github/workflows/test-golang.yaml'
      - 'golang/go.*'
      - '**.go'

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      GOPRIVATE: github.com/carbonrobotics/*
      GO111MODULE: on
      USERNAME: ${{ github.repository_owner }}
      CR_PAT: ${{ secrets.CR_PAT }}
    permissions:
      contents: read

    steps:
      - name: Configure private modules
        run: git config --global url.https://$USERNAME:$<EMAIL>/carbonrobotics/.insteadOf https://github.com/carbonrobotics/

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}
          lfs: true

      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: "1.24"

      - name: Lint Go packages
        run: go fmt ./... && git diff --exit-code
        working-directory: golang

      - name: Vet Go packages
        run: go vet ./...
        working-directory: golang

      - name: Test Go packages
        run: go test -cover ./...
        working-directory: golang
