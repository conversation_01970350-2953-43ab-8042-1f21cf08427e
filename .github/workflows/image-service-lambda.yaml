name: image-service-lambda

on:
  pull_request:
    branches:
      - master
    paths:
      - "golang/go.*"
      - "golang/pkg/**"
      - "golang/lambda/image_service/**"
  push:
    branches:
      - master
      - release/imgsvc-lambda-v*
    tags:
      - imgsvc-lambda-v*
    paths:
      - 'golang/go.*'
      - 'golang/lambda/image_service/**'

env:
  REGISTRY: ghcr.io
  ECR_IMAGE_NAME: image-service-lambda
  GHCR_IMAGE_NAME: ${{ github.repository }}/image-service-lambda
  ECR_REGISTRY: 645516868483.dkr.ecr.us-west-2.amazonaws.com
  DOCKERFILE: golang/lambda/image_service/Dockerfile
  PROJECT_TITLE: carbon image service lambda
  PROJECT_DESCRIPTION: AWS Lambda function for image processing operations

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Register built on date
        run: echo "BUILT_ON=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-west-2
          aws-access-key-id: ${{ secrets.ORG_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.ORG_AWS_SECRET_ACCESS_KEY }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract Metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.ECR_REGISTRY }}/${{ env.ECR_IMAGE_NAME }}
            ${{ env.REGISTRY }}/${{ env.GHCR_IMAGE_NAME }}
          labels: |
            org.opencontainers.image.title=${{ env.PROJECT_TITLE }}
            org.opencontainers.image.description=${{ env.PROJECT_DESCRIPTION }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=ref,event=tag
            type=semver,pattern={{raw}}
            type=sha

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v6
        with:
          secrets: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          build-args: |
            VERSION=${{github.ref_name}}
            COMMIT=${{github.sha}}
            BUILT_ON=${{ env.BUILT_ON }}
          context: ./
          file: ${{ env.DOCKERFILE }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          # --provenance=false is required for buildx to work with ECR
          # https://github.com/docker/buildx/issues/1509#issuecomment-1378538197
          provenance: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
