name: test-js

on:
  pull_request:
    branches:
      - master
    paths:
      - "**.js"
      - "**.ts"
      - "**.tsx"
      - "golang/services/*/ui/**"
      - "golang/services/package.json"
      - "pnpm-lock.yaml"
      - ".github/workflows/test-js.yaml"
  push:
    branches:
      - master
    tags:
      - .*
    paths:
      - "**.js"
      - "**.ts"
      - "**.tsx"
      - "golang/services/*/ui/**"
      - "golang/services/package.json"
      - "pnpm-lock.yaml"
      - ".github/workflows/test-js.yaml"

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}
          lfs: true

      - name: Set up NodeJS
        uses: actions/setup-node@v3
        with:
          node-version: "20.13"

      - name: Install pnpm
        run: npm install -g pnpm@^10.12.2

      - name: Install JS dependencies
        run: pnpm install --frozen-lockfile
        working-directory: golang/services/

      - name: Test Portal UI
        run: pnpm test
        working-directory: golang/services/portal/ui

      - name: Test Robot UI
        run: pnpm test
        working-directory: golang/services/portal/ui
