name: sentry-release

on:
  push:
    branches:
      - "ci/sentry/**"
    tags:
      - portal-v*.*.*

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}
          lfs: true

      - name: Set up NodeJS
        uses: actions/setup-node@v3
        with:
          node-version: "20.13"

      - name: Install pnpm
        run: npm install -g pnpm@^10.12.2

      - name: Install JS dependencies
        run: pnpm install
        working-directory: golang/services/

      - name: Build UI
        run: NODE_OPTIONS="--max_old_space_size=8192" pnpm build
        working-directory: golang/services/portal/ui/

      - name: Create Sentry release
        uses: getsentry/action-release@v1
        env:
          NODE_OPTIONS: "--max_old_space_size=8192"
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: "carbon-robotics"
          SENTRY_PROJECT: "portal-frontend"
        with:
          environment: production
          sourcemaps: "golang/services/portal/ui/dist"
          version: ${{github.ref}}
          ignore_missing: true
