name: comparison-tool

on:
  pull_request:
    branches:
      - master
    paths:
      - 'python/services/comparison-tool/**'
  push:
    branches:
      - master
    tags:
      - cmptool-v*.*.*
    paths:
      - 'python/services/comparison-tool/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/comparison-tool
  DOCKERFILE: python/services/comparison-tool/Dockerfile
  DOCKER_BUILD_CONTEXT: python
  PROJECT_CONTEXT: ./services/comparison-tool
  PROJECT_TITLE: Comparison Tool
  PROJECT_DESCRIPTION: compare tool

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Set Github token
        run: echo "GITHUB_TOKEN=$CR_PAT" >> $GITHUB_ENV

      - name: Register built on date
        run: echo "BUILT_ON=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          labels: |
            org.opencontainers.image.title=${{ env.PROJECT_TITLE }}
            org.opencontainers.image.description=${{ env.PROJECT_DESCRIPTION }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=ref,event=tag
            type=semver,pattern={{raw}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          secrets: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          build-args: |
            VERSION=${{github.ref_name}}
            COMMIT=${{github.sha}}
            BUILT_ON=${{ env.BUILT_ON }}
            PROJECT_CONTEXT=${{ env.PROJECT_CONTEXT }}
          context: ${{ env.DOCKER_BUILD_CONTEXT }}
          file: ${{ env.DOCKERFILE }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
