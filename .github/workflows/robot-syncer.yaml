name: robot-syncer

on:
  pull_request:
    branches:
      - master
      - "release/rosy-*"
      - "ci/rosy/**"
    paths:
      - "golang/go.*"
      - "golang/pkg/**"
      - "golang/services/robot-syncer/**"
  push:
    branches:
      - master
      - "release/rosy-*"
    paths:
      - "golang/go.*"
      - "golang/pkg/**"
      - "golang/services/robot-syncer/**"
    tags:
      - rosy-v*.*.*

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/robot-syncer
  DOCKERFILE: golang/services/robot-syncer/Dockerfile
  PROJECT_TITLE: carbon robot-syncer backend
  PROJECT_DESCRIPTION: api, proxy, and static files for robot-syncer

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Register built on date
        run: echo "BUILT_ON=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          labels: |
            org.opencontainers.image.title=${{ env.PROJECT_TITLE }}
            org.opencontainers.image.description=${{ env.PROJECT_DESCRIPTION }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=ref,event=tag
            type=semver,pattern={{raw}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          secrets: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          build-args: |
            VERSION=${{github.ref_name}}
            COMMIT=${{github.sha}}
            BUILT_ON=${{ env.BUILT_ON }}
            SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
          context: ./
          file: ${{ env.DOCKERFILE }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
