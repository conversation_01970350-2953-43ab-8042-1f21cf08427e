name: ansible

on:
  pull_request:
    branches:
      - master
    paths:
      - 'ansible/**'
  push:
    branches:
      - master
    tags:
      - .*
    paths:
      - 'ansible/**'

jobs:
  ansible:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}

      - uses: actions/setup-python@v3
        with:
          python-version: '3.x'
      - run: pip install --user ansible

      - name: Install Playbook requirements
        run: ansible-galaxy install -r ansible/requirements.yml

      - name: Run Test Playbook Syntax Check
        run: ansible-playbook ansible/tests/tests.yml -i ansible/tests/inventory.ini --syntax-check --verbose