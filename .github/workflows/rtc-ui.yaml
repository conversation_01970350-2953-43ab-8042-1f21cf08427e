name: rtc-ui

on:
  pull_request:
    branches:
      - master
      - "release/rtc-ui-*"
      - "ci/rtc-ui/**"
    paths:
      - "pnpm-lock.yaml"
      - "ui/common/**"
      - "ui/rtc/**"
      - ".github/workflows/rtc-ui.yaml"
  push:
    branches:
      - master
      - "release/rtc-ui-*"
    paths:
      - "pnpm-lock.yaml"
      - "ui/common/**"
      - "ui/rtc/**"
      - ".github/workflows/rtc-ui.yaml"
    tags:
      - rtc-ui-v*.*.*

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/rtc-ui
  DOCKERFILE: ui/rtc/Dockerfile
  DOCKER_BUILD_CONTEXT: .
  PROJECT_CONTEXT: .
  PROJECT_TITLE: RTC UI
  PROJECT_DESCRIPTION: Carbon robotics RTC UI
  NODE_VERSION: 20.13.1

jobs:
  test:
    name: Build, Lint & Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./ui/rtc
    steps:
      - name: Install NodeJS
        uses: actions/setup-node@v2
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Code Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}
          lfs: true

      - name: Install pnpm
        run: npm install -g pnpm@^10.12.2

      - name: Install Dependencies
        run: pnpm install --frozen-lockfile

      - name: Code Linting
        run: pnpm lint

      - name: Testing build
        run: pnpm build

      - name: Running unit tests
        run: pnpm test

  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Register built on date
        run: echo "BUILT_ON=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.CR_PAT }}
          lfs: true

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          labels: |
            org.opencontainers.image.title=${{ env.PROJECT_TITLE }}
            org.opencontainers.image.description=${{ env.PROJECT_DESCRIPTION }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=ref,event=tag
            type=semver,pattern={{raw}}
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          secrets: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          build-args: |
            VERSION=${{github.ref_name}}
            COMMIT=${{github.sha}}
            BUILT_ON=${{ env.BUILT_ON }}
            PROJECT_CONTEXT=${{ env.PROJECT_CONTEXT }}
          context: ${{ env.DOCKER_BUILD_CONTEXT }}
          file: ${{ env.DOCKERFILE }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Update rtc-ui staging image on release
        if: startsWith(github.ref, 'refs/tags')
        uses: clowdhaus/argo-cd-action/@v1.9.0
        with:
          command: app set staging --kustomize-image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{github.ref_name}}
          options: --server argocd.cloud.carbonrobotics.com --auth-token ${{secrets.ARGOCD_STAGE_TOKEN}}
