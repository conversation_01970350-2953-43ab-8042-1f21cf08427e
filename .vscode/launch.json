{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Portal",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/golang/services/portal",
      "envFile": "${workspaceFolder}/golang/services/portal/.env",
      "console": "integratedTerminal"
    },
  ]
}