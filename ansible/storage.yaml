---
- hosts: storage
  gather_facts: yes
  become: yes
  roles:
    - role: geerlingguy.nfs
    - role: cache-mounts

  tasks:
    - name: Disable IPv6 with sysctl
      ansible.posix.sysctl:
        name: "{{ item }}"
        value: "1"
        state: "present"
        reload: "yes"  # Reloads sysctl settings immediately
      with_items:
        - net.ipv6.conf.all.disable_ipv6
        - net.ipv6.conf.default.disable_ipv6
        - net.ipv6.conf.lo.disable_ipv6
