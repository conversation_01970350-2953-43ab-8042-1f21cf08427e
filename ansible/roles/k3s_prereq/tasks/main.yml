---
- name: Enforce minimum Ansible version
  ansible.builtin.assert:
    that:
      - ansible_version.full is version('2.14', '>=')
    msg: "Minimum ansible-core version required is 2.14"

- name: Install Dependent Ubuntu Packages
  when: ansible_distribution in ['Ubuntu']
  ansible.builtin.apt:
    name: policycoreutils  # Used by install script to restore SELinux context
    update_cache: true

- name: Enable IPv4 forwarding
  ansible.posix.sysctl:
    name: net.ipv4.ip_forward
    value: "1"
    state: present
    reload: true

- name: Enable IPv6 forwarding
  ansible.posix.sysctl:
    name: net.ipv6.conf.all.forwarding
    value: "1"
    state: present
    reload: true
  when: ansible_all_ipv6_addresses

- name: Populate service facts
  ansible.builtin.service_facts:

- name: Allow UFW Exceptions
  when:
    - ansible_facts.services['ufw'] is defined
    - ansible_facts.services['ufw'].state == 'running'
  block:
    - name: Get ufw status
      ansible.builtin.command:
        cmd: ufw status
      changed_when: false
      register: ufw_status

    - name: If ufw enabled, open api port
      when:
        - ufw_status['stdout'] == "Status':' active"
      community.general.ufw:
        rule: allow
        port: "{{ api_port }}"
        proto: tcp

    - name: If ufw enabled, open etcd ports
      when:
        - ufw_status['stdout'] == "Status':' active"
        - groups['server'] | length > 1
      community.general.ufw:
        rule: allow
        port: "2379:2381"
        proto: tcp

    - name: If ufw enabled, allow default CIDRs
      when:
        - ufw_status['stdout'] == "Status':' active"
      community.general.ufw:
        rule: allow
        src: '{{ item }}'
      loop: "{{ (cluster_cidr + ',' + service_cidr) | split(',') }}"

- name: Allow Firewalld Exceptions
  when:
    - ansible_facts.services['firewalld.service'] is defined
    - ansible_facts.services['firewalld.service'].state == 'running'
  block:
    - name: If firewalld enabled, open api port
      ansible.posix.firewalld:
        port: "{{ api_port }}/tcp"
        zone: trusted
        state: enabled
        permanent: true
        immediate: true

    - name: If firewalld enabled, open etcd ports
      when: groups['server'] | length > 1
      ansible.posix.firewalld:
        port: "2379-2381/tcp"
        zone: trusted
        state: enabled
        permanent: true
        immediate: true

    - name: If firewalld enabled, allow default CIDRs
      ansible.posix.firewalld:
        source: "{{ item }}"
        zone: trusted
        state: enabled
        permanent: true
        immediate: true
      loop: "{{ (cluster_cidr + ',' + service_cidr) | split(',') }}"

- name: Add br_netfilter to /etc/modules-load.d/
  ansible.builtin.copy:
    content: "br_netfilter"
    dest: /etc/modules-load.d/br_netfilter.conf
    mode: "u=rw,g=,o="
  when: (ansible_os_family == 'RedHat' or ansible_distribution == 'Archlinux')

- name: Load br_netfilter
  community.general.modprobe:
    name: br_netfilter
    state: present
  when: (ansible_os_family == 'RedHat' or ansible_distribution == 'Archlinux')

- name: Set bridge-nf-call-iptables (just to be sure)
  ansible.posix.sysctl:
    name: "{{ item }}"
    value: "1"
    state: present
    reload: true
  when: (ansible_os_family == 'RedHat' or ansible_distribution == 'Archlinux')
  loop:
    - net.bridge.bridge-nf-call-iptables
    - net.bridge.bridge-nf-call-ip6tables

- name: Check for Apparmor existence
  ansible.builtin.stat:
    path: /sys/module/apparmor/parameters/enabled
  register: apparmor_enabled

- name: Check if Apparmor is enabled
  when: apparmor_enabled.stat.exists
  ansible.builtin.command: cat /sys/module/apparmor/parameters/enabled
  register: apparmor_status
  changed_when: false

- name: Install Apparmor Parser [Suse]
  when:
    - ansible_os_family == 'Suse'
    - apparmor_status is defined
    - apparmor_status.stdout == "Y"
  ansible.builtin.package:
    name: apparmor-parser
    state: present

- name: Install Apparmor Parser [Debian]
  when:
    - ansible_distribution == 'Debian'
    - ansible_facts['distribution_major_version'] == "11"
    - apparmor_status is defined
    - apparmor_status.stdout == "Y"
  ansible.builtin.package:
    name: apparmor
    state: present

- name: Gather the package facts
  ansible.builtin.package_facts:
    manager: auto

# Iptables v1.8.0-1.8.4 have a specific bug with K3s. https://github.com/k3s-io/k3s/issues/3117
- name: If iptables v1.8.0-1.8.4, warn user  # noqa ignore-errors
  when:
    - ansible_facts.packages['iptables'] is defined
    - ansible_facts.packages['iptables'][0]['version'] is version('1.8.5', '<')
    - ansible_facts.packages['iptables'][0]['version'] is version('1.7.9', '>')
  ansible.builtin.fail:
    msg:
      - "Warning: Iptables {{ ansible_facts.packages['iptables'][0]['version'] }} found."
      - "Add '--prefer-bundled-bin' to extra_server_args variable to use the bundled iptables binary."
  ignore_errors: true

- name: Add /usr/local/bin to sudo secure_path
  ansible.builtin.lineinfile:
    line: 'Defaults    secure_path = /sbin:/bin:/usr/sbin:/usr/bin:/usr/local/bin'
    regexp: "Defaults(\\s)*secure_path(\\s)*="
    state: present
    insertafter: EOF
    path: /etc/sudoers
    validate: 'visudo -cf %s'
  when: ansible_os_family == 'RedHat'

- name: Setup alternative K3s directory
  when:
    - k3s_server_location is defined
    - k3s_server_location != "/var/lib/rancher/k3s"
  block:
    - name: Make rancher directory
      ansible.builtin.file:
        path: "/var/lib/rancher"
        mode: 0755
        state: directory
    - name: Create symlink
      ansible.builtin.file:
        dest: /var/lib/rancher/k3s
        src: "{{ k3s_server_location }}"
        force: true
        state: link

- name: Setup extra manifests
  when: extra_manifests is defined
  block:
    - name: Make manifests directory
      ansible.builtin.file:
        path: "/var/lib/rancher/k3s/server/manifests"
        mode: 0700
        state: directory
    - name: Copy manifests
      ansible.builtin.copy:
        src: "{{ item }}"
        dest: "/var/lib/rancher/k3s/server/manifests"
        mode: 0600
      loop: "{{ extra_manifests }}"

- name: Setup optional private registry configuration
  when: registries_config_yaml is defined
  block:
    - name: Make k3s config directory
      ansible.builtin.file:
        path: "/etc/rancher/k3s"
        mode: 0755
        state: directory
    - name: Copy config values
      ansible.builtin.copy:
        content: "{{ registries_config_yaml }}"
        dest: "/etc/rancher/k3s/registries.yaml"
        mode: 0644
