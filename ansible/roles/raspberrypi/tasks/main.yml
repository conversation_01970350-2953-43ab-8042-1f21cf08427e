---
- name: Test for raspberry pi /proc/cpuinfo
  ansible.builtin.command: grep -E "Raspberry Pi|BCM2708|BCM2709|BCM2835|BCM2836" /proc/cpuinfo
  register: grep_cpuinfo_raspberrypi
  failed_when: false
  changed_when: false

- name: Test for raspberry pi /proc/device-tree/model
  ansible.builtin.command: grep -E "Raspberry Pi" /proc/device-tree/model
  register: grep_device_tree_model_raspberry<PERSON>
  failed_when: false
  changed_when: false

- name: Set raspberry_pi fact to true
  ansible.builtin.set_fact:
    raspberry_pi: true
  when:
    grep_cpuinfo_raspberrypi.rc == 0 or grep_device_tree_model_raspberrypi.rc == 0

- name: Set detected_distribution to Raspbian
  ansible.builtin.set_fact:
    detected_distribution: Raspbian
  when: >
    raspberry_pi|default(false) and
    ( ansible_facts.lsb.id|default("") == "Raspbian" or
      ansible_facts.lsb.description|default("") is match("[Rr]aspbian.*") )

- name: Set detected_distribution to Debian
  ansible.builtin.set_fact:
    detected_distribution: Debian
  when: >
    raspberry_pi|default(false) and
    ( ansible_facts.lsb.id|default("") == "Debian" or
      ansible_facts.lsb.description|default("") is match("Debian") )

- name: Set detected_distribution to ArchLinux (ARM64)
  ansible.builtin.set_fact:
    detected_distribution: Archlinux
  when:
    - ansible_facts.architecture is search("aarch64")
    - raspberry_pi|default(false)
    - ansible_facts.os_family is match("Archlinux")

- name: Execute OS related tasks on the Raspberry Pi
  ansible.builtin.include_tasks: "{{ item }}"
  with_first_found:
    - "prereq/{{ detected_distribution }}.yml"
    - "prereq/{{ ansible_distribution }}-{{ ansible_distribution_major_version }}.yml"
    - "prereq/{{ ansible_distribution }}.yml"
    - "prereq/default.yml"
  when:
    - raspberry_pi|default(false)
