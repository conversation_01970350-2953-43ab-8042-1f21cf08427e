---
- name: Check if /boot/firmware/cmdline.txt exists
  ansible.builtin.stat:
    path: /boot/firmware/cmdline.txt
  register: boot_firmware_cmdline_txt

- name: Enable cgroup via boot commandline if not already enabled
  ansible.builtin.lineinfile:
    path: "{{ (boot_firmware_cmdline_txt.stat.exists) | ternary('/boot/firmware/cmdline.txt', '/boot/cmdline.txt') }}"
    backrefs: true
    regexp: '^((?!.*\bcgroup_enable=cpuset cgroup_memory=1 cgroup_enable=memory\b).*)$'
    line: '\1 cgroup_enable=cpuset cgroup_memory=1 cgroup_enable=memory'
  notify: Reboot Pi

- name: Gather the package facts
  ansible.builtin.package_facts:
    manager: auto

# IPtables versions 1.6.1 and older have problems with K3s, so we force the use of
# iptables-legacy in that case.
- name: If old iptables found, change to iptables-legacy
  when:
    - ansible_facts.packages['iptables'] is defined
    - ansible_facts.packages['iptables'][0]['version'] is version('1.6.2', '<')
  block:
    - name: Iptables version on node
      ansible.builtin.debug:
        msg: "iptables version {{ ansible_facts.packages['iptables'][0]['version'] }} found"

    - name: Flush iptables before changing to iptables-legacy
      ansible.builtin.iptables:
        flush: true
      changed_when: false   # iptables flush always returns changed

    - name: Changing to iptables-legacy
      community.general.alternatives:
        path: /usr/sbin/iptables-legacy
        name: iptables
      register: ip4_legacy

    - name: Changing to ip6tables-legacy
      community.general.alternatives:
        path: /usr/sbin/ip6tables-legacy
        name: ip6tables
      register: ip6_legacy
