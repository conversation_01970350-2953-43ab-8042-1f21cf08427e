---
- name: Check for Airgap
  when: airgap_dir is defined
  block:

    - name: Verify Ansible meets airgap version requirements.
      ansible.builtin.assert:
        that: "ansible_version.full is version_compare('2.12', '>=')"
        msg: "The Airgap role requires at least ansible-core 2.12"

    - name: Download k3s install script
      become: false
      delegate_to: localhost
      ansible.builtin.get_url:
        url: https://get.k3s.io/
        timeout: 120
        dest: "{{ airgap_dir }}/k3s-install.sh"
        mode: 0755

    - name: Distribute K3s install script
      ansible.builtin.copy:
        src: "{{ airgap_dir }}/k3s-install.sh"
        dest: /usr/local/bin/k3s-install.sh
        owner: root
        group: root
        mode: 0755

    - name: Distribute K3s binary
      ansible.builtin.copy:
        src: "{{ airgap_dir }}/k3s"
        dest: /usr/local/bin/k3s
        owner: root
        group: root
        mode: 0755

    - name: Distribute K3s SELinux RPM
      ansible.builtin.copy:
        src: "{{ item }}"
        dest: /tmp/
        owner: root
        group: root
        mode: 0755
      with_fileglob:
        - "{{ airgap_dir }}/k3s-selinux*.rpm"
      register: selinux_copy
      ignore_errors: true

    - name: Install K3s SELinux RPM
      when:
        - ansible_os_family == 'RedHat'
        - selinux_copy.skipped is false
      ansible.builtin.yum:
        name: "{{ selinux_copy.results[0].dest }}"
        state: present
        disable_gpg_check: true

    - name: Make images directory
      ansible.builtin.file:
        path: "/var/lib/rancher/k3s/agent/images/"
        mode: 0755
        state: directory

    - name: Determine Architecture
      ansible.builtin.set_fact:
        k3s_arch: "{{ ansible_architecture }}"

    - name: Distribute K3s amd64 images
      when: ansible_architecture == 'x86_64'
      ansible.builtin.copy:
        src: "{{ item }}"
        dest: /var/lib/rancher/k3s/agent/images/{{ item | basename }}
        owner: root
        group: root
        mode: 0755
      with_first_found:
        - files:
            - "{{ airgap_dir }}/k3s-airgap-images-amd64.tar.zst"
            - "{{ airgap_dir }}/k3s-airgap-images-amd64.tar.gz"
            - "{{ airgap_dir }}/k3s-airgap-images-amd64.tar"
          skip: true

    - name: Distribute K3s arm64 images
      when: ansible_architecture == 'aarch64'
      ansible.builtin.copy:
        src: "{{ item }}"
        dest: /var/lib/rancher/k3s/agent/images/{{ item | basename }}
        owner: root
        group: root
        mode: 0755
      with_first_found:
        - files:
            - "{{ airgap_dir }}/k3s-airgap-images-arm64.tar.zst"
            - "{{ airgap_dir }}/k3s-airgap-images-arm64.tar.gz"
            - "{{ airgap_dir }}/k3s-airgap-images-arm64.tar"
          skip: true

    - name: Distribute K3s arm images
      when: ansible_architecture == 'armv7l'
      ansible.builtin.copy:
        src: "{{ item }}"
        dest: /var/lib/rancher/k3s/agent/images/{{ item | basename }}
        owner: root
        group: root
        mode: 0755
      with_first_found:
        - files:
            - "{{ airgap_dir }}/k3s-airgap-images-arm.tar.zst"
            - "{{ airgap_dir }}/k3s-airgap-images-arm.tar.gz"
            - "{{ airgap_dir }}/k3s-airgap-images-arm.tar"
          skip: true

    - name: Run K3s Install [server]
      ansible.builtin.command:
        cmd: /usr/local/bin/k3s-install.sh
      environment:
        INSTALL_K3S_SKIP_START: "true"
        INSTALL_K3S_SKIP_DOWNLOAD: "true"
      changed_when: true

    - name: Run K3s Install [agent]
      ansible.builtin.command:
        cmd: /usr/local/bin/k3s-install.sh
      environment:
        INSTALL_K3S_SKIP_START: "true"
        INSTALL_K3S_SKIP_DOWNLOAD: "true"
        INSTALL_K3S_EXEC: "agent"
      changed_when: true
