---
- name: Set grub iommu=soft
  ansible.builtin.lineinfile:
    path: /etc/default/grub
    regexp: '^GRUB_CMDLINE_LINUX_DEFAULT='
    line: 'GRUB_CMDLINE_LINUX_DEFAULT="iommu=soft"'
  notify: update grub

- name: Change default target to multi-user.target
  ansible.builtin.file:
    src: /lib/systemd/system/multi-user.target
    dest: /etc/systemd/system/default.target
    state: link

- name: Copy 'disable-turbo-boost.service'
  ansible.builtin.copy:
    src: ../files/disable-turbo-boost.service
    dest: /etc/systemd/system/disable-turbo-boost.service
    owner: root
    group: root
    mode: '0644'

- name: Enable and Start 'disable-turbo-boost.service'
  ansible.builtin.systemd:
    name: disable-turbo-boost
    daemon_reload: yes
    state: started
    enabled: yes

- name: Add Docker GPG apt Key
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present

- name: Add Docker Repository
  apt_repository:
    repo: deb https://download.docker.com/linux/ubuntu focal stable
    state: present

- name: Add Nvidia GPG apt Key
  apt_key:
    url: https://nvidia.github.io/libnvidia-container/gpgkey
    state: present

- name: Add Nvidia Container Repository
  apt_repository:
    repo: "{{item}}"
    state: present
    filename: nvidia-docker
  with_items:
   - 'deb https://nvidia.github.io/libnvidia-container/stable/deb/$(ARCH) /'

- name: Installing apt packages
  apt:
    update_cache: yes
    pkg:
      - ca-certificates
      - curl
      - containerd.io
      - lsb-release
      - gnupg
      - docker-ce
#      - nvidia-docker2
      - nfs-common
      - nvidia-settings
      - nvidia-container-toolkit
      - nvidia-container-runtime
      - "xserver-xorg-video-nvidia-{{nvidia_driver_branch}}-server"
      - python3-pip
      - git-lfs
      - silversearcher-ag
      - xorg
      - openbox
      - python3-openssl
      - nvme-cli
      - "linux-headers-{{ ansible_kernel }}"

- name: Upgrade pip
  pip:
    name: pip
    extra_args: --upgrade

- name: Installing apt kernel packages
  apt:
    update_cache: yes
    pkg:
      - linux-image-generic-hwe-20.04
  notify: reboot

- name: Add the nvme-tcp module
  community.general.modprobe:
    name: nvme_tcp
    persistent: present
    state: present

- name: Add the nvme_fabrics module
  community.general.modprobe:
    name: nvme_fabrics
    persistent: present
    state: present

- name: Prevent Unattended Upgrades
  apt:
    name: unattended-upgrades
    state: absent

- name: Autoremove apt packages
  apt:
    autoremove: yes

- name: Copy docker 'daemon.json'
  ansible.builtin.copy:
    src: ../files/docker-daemon.json
    dest: /etc/docker/daemon.json
    owner: root
    group: root
    mode: '0644'

- name: Installing python packages
  pip:
    name:
#      - docker-compose
      - matplotlib
      - boto3
      - opencv-python
      - coolgpus
      - pyopenssl

- name: Copy 'max-gpu-fans.service'
  ansible.builtin.copy:
    src: ../files/max-gpu-fans.service
    dest: /etc/systemd/system/max-gpu-fans.service
    owner: root
    group: root
    mode: '0644'

- name: Enable and Start 'max-gpu-fans.service'
  ansible.builtin.systemd:
    name: max-gpu-fans
    daemon_reload: yes
    state: started
    enabled: yes

- name: Enable Huge Page support (1024)
  sysctl:
    name: vm.nr_hugepages
    value: 1024
    state: present
  notify: reboot

- name: Disable IPv6 with sysctl
  ansible.posix.sysctl:
    name: "{{ item }}"
    value: "1"
    state: "present"
    reload: "yes"  # Reloads sysctl settings immediately
  with_items:
    - net.ipv6.conf.all.disable_ipv6
    - net.ipv6.conf.default.disable_ipv6
    - net.ipv6.conf.lo.disable_ipv6

- name: "Mounting Extra disks"
  include_tasks: mount_disks.yaml
  when: mount_disks is defined
  loop: "{{ mount_disks }}"
  loop_control:
    loop_var: disk
