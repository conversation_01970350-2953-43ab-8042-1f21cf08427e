---
- name: "installing dependencies"
  apt:
    name: parted
    update_cache: yes

- name: Create First Cache Partition
  community.general.parted:
    device: "{{cache_drive1}}"
    number: 1
    label: gpt
    part_type: logical
    state: present

- name: "Create a ext4 filesystem on {{cache_drive1}}1"
  community.general.filesystem:
    fstype: ext4
    dev: "{{cache_drive1}}1"

- name: "Ensure /cache/1 exists"
  ansible.builtin.file:
    path: /cache/1
    state: directory
    mode: '0777'

- name: Mount First Cache
  ansible.posix.mount:
    path: /cache/1
    src: "{{cache_drive1}}1"
    fstype: ext4
    state: present

- name: Create Second Cache Partition
  community.general.parted:
    device: "{{cache_drive2}}"
    number: 1
    label: gpt
    part_type: logical
    state: present

- name: "Create a ext4 filesystem on {{cache_drive2}}1"
  community.general.filesystem:
    fstype: ext4
    dev: "{{cache_drive2}}1"

- name: "Ensure /cache/2 exists"
  ansible.builtin.file:
    path: /cache/2
    state: directory
    mode: '0777'

- name: Mount First Cache
  ansible.posix.mount:
    path: /cache/2
    src: "{{cache_drive2}}1"
    fstype: ext4
    state: present