---
- name: Reboot cluster servers staggered
  hosts: server
  become: true
  gather_facts: true
  serial: 1
  tasks:
    - name: Confirm
      pause:
        prompt: Are you sure you want to Reboot master nodes (yes/no)?
      register: confirm_continue
      failed_when: not (confirm_continue.user_input | bool)

    - name: Reboot
      ansible.builtin.reboot:
        test_command: kubectl get nodes

- name: Reboot cluster agents staggered
  hosts: agent
  become: true
  gather_facts: true
  serial: 1
  tasks:
    - name: Confirm
      pause:
        prompt: Are you sure you want to Reboot agent nodes (yes/no)?
      register: confirm_continue
      failed_when: not (confirm_continue.user_input | bool)

    - name: Reboot
      ansible.builtin.reboot:
