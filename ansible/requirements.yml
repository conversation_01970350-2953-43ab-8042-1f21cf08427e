---
# reference: https://galaxy.ansible.com/docs/using/installing.html
# install with `$ ansible-galaxy install -r requirements.yml`

# from galaxy
roles:
- name: nvidia.nvidia_driver # https://github.com/NVIDIA/ansible-role-nvidia-driver
- name: geerlingguy.nfs # https://galaxy.ansible.com/geerlingguy/nfs
- name: geerlingguy.docker # https://galaxy.ansible.com/ui/standalone/roles/geerlingguy/docker/
- name: geerlingguy.swap # https://galaxy.ansible.com/ui/standalone/roles/geerlingguy/swap/
- name: mrlesmithjr.ansible-mdadm # https://galaxy.ansible.com/ui/standalone/roles/mrlesmithjr/ansible-mdadm/
- name: geerlingguy.node_exporter # https://galaxy.ansible.com/ui/standalone/roles/geerlingguy/node_exporter/
# from GitHub
#- src: https://github.com/bennojoy/nginx

collections:
- name: community.general # https://galaxy.ansible.com/community/general?extIdCarryOver=true&sc_cid=701f2000001OH7YAAW
- name: ansible.posix # https://galaxy.ansible.com/ansible/posix?extIdCarryOver=true&sc_cid=701f2000001OH7YAAW
- name: community.docker # https://galaxy.ansible.com/ui/repo/published/community/docker/?extIdCarryOver=true&sc_cid=701f2000001OH7YAAW

# from GitHub, overriding the name and specifying a specific tag
#- src: https://github.com/bennojoy/nginx
#  version: master
#  name: nginx_role

# from a webserver, where the role is packaged in a tar.gz
#- src: https://some.webserver.example.com/files/master.tar.gz
#  name: http-role

# from Bitbucket
#- src: git+http://bitbucket.org/willthames/git-ansible-galaxy
#  version: v1.4

# from Bitbucket, alternative syntax and caveats
#- src: http://bitbucket.org/willthames/hg-ansible-galaxy
#  scm: hg

# from GitLab or other git-based scm
#- src: **********************:mygroup/ansible-base.git
#  scm: git
#  version: "0.1"  # quoted, so YAML doesn't parse this as a floating-point value