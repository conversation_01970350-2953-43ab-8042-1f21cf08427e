---
- hosts: franken<PERSON>_cache_server
  gather_facts: yes
  become: yes

  roles:
    - role: geerlingguy.docker
    - role: geerlingguy.node_exporter

  tasks:
    - name: Installing apt packages
      apt:
        update_cache: yes
        pkg:
          - ca-certificates
          - curl

    - name: Prevent Unattended Upgrades
      apt:
        name: unattended-upgrades
        state: absent

    - name: Autoremove apt packages
      apt:
        autoremove: yes

    - name: Disable IPv6 with sysctl
      ansible.posix.sysctl:
        name: "{{ item }}"
        value: "1"
        state: "present"
        reload: "yes"  # Reloads sysctl settings immediately
      with_items:
        - net.ipv6.conf.all.disable_ipv6
        - net.ipv6.conf.default.disable_ipv6
        - net.ipv6.conf.lo.disable_ipv6

    - name: "Create a ext4 filesystem on {{cache_device}}"
      community.general.filesystem:
        fstype: ext4
        dev: "{{cache_device}}"

    - name: Mount cache device
      ansible.posix.mount:
        path: /cache
        src: "{{cache_device}}"
        fstype: ext4
        state: mounted

    - name: "Create a ext4 filesystem on {{cache2_device}}"
      community.general.filesystem:
        fstype: ext4
        dev: "{{cache2_device}}"

    - name: Mount cache device
      ansible.posix.mount:
        path: /cache2
        src: "{{cache2_device}}"
        fstype: ext4
        state: mounted
