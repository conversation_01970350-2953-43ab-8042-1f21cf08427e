# Carbon Robotics Ansible Playbooks

This directory should conform to the recommended layout found [here](https://docs.ansible.com/ansible/latest/user_guide/sample_setup.html#sample-directory-layout)

In order to run ansible, you must enable passwordless auth via ssh by setting a public key for the `carbon` user.
A typical run from a local system would look like the following from the `ansible` directory

```
# install requirements
$ ansible-galaxy install -r requirements.yml

# run target playbook with target inventory
$ ansible-playbook -i inventory/<inventory>.ini <play>.yml --ask-become-pass
```

### Optionals

* Disable host key checking by setting env var 
  `ANSIBLE_HOST_KEY_CHECKING=False`
