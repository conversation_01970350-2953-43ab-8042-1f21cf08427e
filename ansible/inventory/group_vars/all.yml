---
ansible_port: 22
ansible_user: carbon
k3s_version: v1.32.2+k3s1
token: "XCIvwjdRmALS63DwHQTzTQPRTpLXFWbh94GFBPLTLyeJYtr3EfFUsGwtPQzAwd9X"
api_endpoint: "{{ hostvars[groups['server'][0]]['ansible_host'] | default(groups['server'][0]) }}"
extra_server_args: "--prefer-bundled-bin --embedded-registry --disable-default-registry-endpoint"
extra_agent_args: "--disable-default-registry-endpoint"
server_config_yaml:
  cluster-cidr: "*********/16"
  service-cidr: "*********/16"
  tls-san-security: false
registries_config_yaml: |
  mirrors:
    docker.io:
      endpoint:
      - http://storage1.dc.carbonrobotics.com:5000
  configs:
    docker.io:
      tls:
        insecure_skip_verify: true

kubeconfig: "./kubeconfig"

# k3s ref -> https://github.com/k3s-io/k3s-ansible/blob/master/inventory-sample.yml

nfs_exports:
  - "/space *(rw,no_root_squash)"

nvidia_driver_branch: "535"
