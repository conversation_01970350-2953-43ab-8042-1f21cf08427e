name: portal
services:
  veselka:
    build:
      dockerfile: ./Dockerfile.veselka.dev
    platform: linux/amd64
    deploy:
      mode: global
    container_name: portal-veselka
    volumes:
      - ./:/portal
      - ~/.kube:/root/.kube
      - ~/.aws:/root/.aws
    ports:
      - ${VESELKA_PORT}:8080
    environment:
      VESELKA_ENV: ${VESELKA_ENV}

  robot-syncer:
    build:
      dockerfile: ./Dockerfile.robot-syncer.dev
    platform: linux/amd64
    deploy:
      mode: global
    container_name: portal-robot-syncer
    volumes:
      - ./:/portal
      - ~/.kube:/root/.kube
      - ~/.aws:/root/.aws
    ports:
      - ${ROBOT_SYNCER_PORT}:8080
    environment:
      ROBOT_SYNCER_ENV: ${ROBOT_SYNCER_ENV}

  db:
    image: ghcr.io/baosystems/postgis
    container_name: portal-db
    deploy:
      mode: global
    ports:
      - ${PSQL_PORT}:5432
    environment:
      POSTGRES_DB: ${PSQL_DB}
      POSTGRES_PASSWORD: ${PSQL_PASSWORD}
      POSTGRES_USER: ${PSQL_USER}
    volumes:
      - /tmp:/tmp
      - ./db:/db
      - ./db/init:/docker-entrypoint-initdb.d

  server:
    build:
      context: ../../
      dockerfile: ./services/portal/Dockerfile.server.dev
      secrets:
        - GITHUB_TOKEN
        - SENTRY_AUTH_TOKEN
    container_name: portal-server
    deploy:
      mode: global
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - ${SERVER_PORT}:${SERVER_PORT}
      - ${GRPC_PORT}:${GRPC_PORT}
    environment:
      AUTH0_GRPC_AUDIENCE: ${AUTH0_GRPC_AUDIENCE}
      AUTH0_GRPC_CLIENT_ID: ${AUTH0_GRPC_CLIENT_ID}
      AUTH0_GRPC_CLIENT_SECRET: ${AUTH0_GRPC_CLIENT_SECRET}
      AUTH0_REST_AUDIENCE: ${AUTH0_REST_AUDIENCE}
      AUTH0_REST_CLIENT_ID: ${AUTH0_REST_CLIENT_ID}
      AUTH0_REST_CLIENT_SECRET: ${AUTH0_REST_CLIENT_SECRET}
      AUTH0_SERVER_ID: ${AUTH0_SERVER_ID}
      AUTH0_SERVER_SECRET: ${AUTH0_SERVER_SECRET}
      AUTH0_TENANT_DOMAIN: ${AUTH0_TENANT_DOMAIN}
      AWS_BUCKET: ${AWS_BUCKET}
      AWS_CREDENTIALS_FILE: ${AWS_CREDENTIALS_FILE}
      CONFIG_ENV: ${CONFIG_ENV}
      CONFIG_URL: ${CONFIG_URL}
      DEV_EMAILS: ${DEV_EMAILS}
      DIST_PATH: ${DIST_PATH}
      EMAIL_FROM_DOMAIN: ${EMAIL_FROM_DOMAIN}
      ENVIRONMENT: ${ENVIRONMENT}
      JIRA_WEBHOOK_TOKEN: ${JIRA_WEBHOOK_TOKEN}
      LOG_LEVEL: ${LOG_LEVEL}
      PAGERDUTY_API_KEY: ${PAGERDUTY_API_KEY:-}
      PAGERDUTY_INTEGRATION_KEY: ${PAGERDUTY_INTEGRATION_KEY:-}
      PERF_MODE: ${PERF_MODE}
      POSTGRES_PASSWORD: ${PSQL_PASSWORD}
      POSTGRES_USER: ${PSQL_USER}
      PSQL_CONNECTION: ${PSQL_CONNECTION}
      R3_ACCESS_KEY_ID: ${R3_ACCESS_KEY_ID}
      R3_ACCOUNT_ID: ${R3_ACCOUNT_ID}
      R3_SECRET_ACCESS_KEY: ${R3_SECRET_ACCESS_KEY}
      REACT_APP_STREAM_API_KEY: ${REACT_APP_STREAM_API_KEY}
      ROBOT_SYNCER_URL: ${ROBOT_SYNCER_URL}
      ROOT_URL: ${ROOT_URL}
      RTC_JOBS_TARGET: ${RTC_JOBS_TARGET}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY:-}
      SENTRY_DSN: ${SENTRY_DSN}
      SERVER_PORT: ${SERVER_PORT}
      GRPC_PORT: ${GRPC_PORT}
      SHARED_CONFIG_SCHEMAS_PATH: ${SHARED_CONFIG_SCHEMAS_PATH}
      SLACK_OAUTH_TOKEN: ${SLACK_OAUTH_TOKEN}
      STREAM_API_SECRET: ${STREAM_API_SECRET}
      TABLEAU_WEBHOOK_TOKEN: ${TABLEAU_WEBHOOK_TOKEN}
      TWILIO_API_KEY: ${TWILIO_API_KEY:-}
      VESELKA_URL: ${VESELKA_URL}
    volumes:
      - ../../../:/cloud
      - ./ui/dist:/dist
      - ${AWS_CREDENTIALS_FILE}:${AWS_CREDENTIALS_FILE}
    networks:
      - default
      - carbon

  ui:
    build:
      context: ../../../
      dockerfile: ./golang/services/portal/Dockerfile.ui.dev
    container_name: portal-ui
    deploy:
      mode: global
    ports:
      - ${CLIENT_PORT}:7000
    environment:
      CLIENT_PORT: ${CLIENT_PORT}
      ENVIRONMENT: ${ENVIRONMENT}
      REACT_APP_AUTH0_AUDIENCE: ${REACT_APP_AUTH0_AUDIENCE}
      REACT_APP_AUTH0_AUTH_DOMAIN: ${REACT_APP_AUTH0_AUTH_DOMAIN}
      REACT_APP_AUTH0_REST_CLIENT_ID: ${REACT_APP_AUTH0_REST_CLIENT_ID}
      REACT_APP_GOOGLE_MEASUREMENT_ID: ${REACT_APP_GOOGLE_MEASUREMENT_ID}
      REACT_APP_IDM_URL: ${REACT_APP_IDM_URL}
      REACT_APP_MAPBOX_ACCESS_TOKEN: ${REACT_APP_MAPBOX_ACCESS_TOKEN}
      REACT_APP_MUI_LICENSE_KEY: ${REACT_APP_MUI_LICENSE_KEY}
      REACT_APP_RTC_LOCATOR_URL: ${REACT_APP_RTC_LOCATOR_URL}
      REACT_APP_SENTRY_DSN: ${REACT_APP_SENTRY_DSN}
      REACT_APP_SENTRY_ENVIRONMENT: ${REACT_APP_SENTRY_ENVIRONMENT}
      REACT_APP_STREAM_API_KEY: ${REACT_APP_STREAM_API_KEY}
      REACT_APP_IMAGE_SERVICE_URL: ${REACT_APP_IMAGE_SERVICE_URL}
      REACT_APP_VESELKA_URL: ${REACT_APP_VESELKA_URL}
      REACT_APP_RTC_JOBS_URL: ${REACT_APP_RTC_JOBS_URL}
      REACT_APP_FEATURE_LIGHT_DARK_THEME: ${REACT_APP_FEATURE_LIGHT_DARK_THEME:-false}
      ROOT_URL: ${ROOT_URL}
      SERVER_URL: http://server:${SERVER_PORT}
      # For Git operations in dev server config; can't discover automatically
      # because the `portal/ui` and `cloud/.git` directories are in separate
      # overlay filesystems.
      GIT_DIR: /cloud/.git
    volumes:
      - ../../../.git:/cloud/.git:ro
      - ../../../localization-cloud:/cloud/localization-cloud:ro
      - ../../../protos:/cloud/protos:ro
      - ../../../ui/common:/cloud/ui/common:ro
      - ./ui:/cloud/golang/services/portal/ui

networks:
  # Used to talk to containers running in other Docker Compose projects: e.g.,
  # a local instance of the RTC Jobs service.
  carbon:
    name: carbon_network

secrets:
  GITHUB_TOKEN:
    environment: GITHUB_TOKEN
  SENTRY_AUTH_TOKEN:
    environment: SENTRY_AUTH_TOKEN
