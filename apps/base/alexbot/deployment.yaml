apiVersion: apps/v1
kind: Deployment
metadata:
  name: alexbot
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: alexbot
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: alexbot
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: alexbot
          image: ghcr.io/carbonrobotics/alexbot/alexbot:master
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: alexbot
            - secretRef:
                name: alexbot
          imagePullPolicy: Always
          resources:
            requests:
              memory: "512Mi"
            limits:
              memory: "2Gi"
              ephemeral-storage: "512Mi"
