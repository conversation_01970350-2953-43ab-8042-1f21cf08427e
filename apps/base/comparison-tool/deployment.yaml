apiVersion: apps/v1
kind: Deployment
metadata:
  name: comparison-tool
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: comparison-tool
  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: comparison-tool
    spec:
      serviceAccountName: comparison-tool-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: comparison-tool
          image: ghcr.io/carbonrobotics/cloud/comparison-tool:unknown
          envFrom:
            - configMapRef:
                name: comparison-tool
            - secretRef:
                name: comparison-tool
          imagePullPolicy: Always
          ports:
            - containerPort: 8501
          resources:
            requests:
              memory: "6Gi"
            limits:
              memory: "16Gi"
              ephemeral-storage: "10Gi"
          volumeMounts:
            - name: ephemeral-data
              mountPath: /data
      volumes:
        - name: ephemeral-data
          ephemeral:
            volumeClaimTemplate:
              metadata:
                labels:
                  app.kubernetes.io/name: comparison-tool
              spec:
                accessModes: [ "ReadWriteOnce" ]
                storageClassName: "ebs-sc"
                resources:
                  requests:
                    storage: 500Gi