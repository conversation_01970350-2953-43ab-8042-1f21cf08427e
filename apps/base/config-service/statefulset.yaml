apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: config
spec:
  selector:
    matchLabels:
      app: config
  serviceName: "config"
  replicas: 1
  revisionHistoryLimit: 2
  template:
    metadata:
      labels:
        app: config
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: config
          image: ghcr.io/carbonrobotics/robot/common:unknown
          imagePullPolicy: Always
          command:
            - bin/cloud_config_service
          ports:
            - containerPort: 61001
              protocol: TCP
          volumeMounts:
            - mountPath: /data
              name: config-efs-claim
          resources:
            requests:
              memory: "8Gi"
            limits:
              memory: "8Gi"
  volumeClaimTemplates:
    - metadata:
        name: config-efs-claim
      spec:
        accessModes: [ "ReadWriteMany" ]
        storageClassName: "efs-sc"
        resources:
          requests:
            storage: 4Gi
