apiVersion: apps/v1
kind: Deployment
metadata:
  name: carbon-panelytics
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: carbon-panelytics
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: carbon-panelytics
    spec:
      serviceAccountName: carbon-panelytics-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: panelytics
          image: ghcr.io/carbonrobotics/cloud/carbon-panelytics:v0.0.383
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: carbon-panelytics
            - secretRef:
                name: carbon-panelytics
          imagePullPolicy: Always
          ports:
            - containerPort: 5006
          resources:
            requests:
              memory: "1Gi"
            limits:
              memory: "8Gi"
              ephemeral-storage: "100Mi"
          volumeMounts:
            - name: ephemeral-data
              mountPath: /data
        - name: oauth2-proxy
          image: quay.io/oauth2-proxy/oauth2-proxy:v7.5.1
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: carbon-panelytics
          ports:
            - containerPort: 4181
      volumes:
        - name: ephemeral-data
          ephemeral:
            volumeClaimTemplate:
              metadata:
                labels:
                  app.kubernetes.io/name: carbon-panelytics
              spec:
                accessModes: [ "ReadWriteOnce" ]
                storageClassName: "ebs-sc"
                resources:
                  requests:
                    storage: 50Gi
