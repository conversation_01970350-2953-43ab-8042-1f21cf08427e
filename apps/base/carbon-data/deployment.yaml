apiVersion: apps/v1
kind: Deployment
metadata:
  name: carbon-data
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: carbon-data
  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: carbon-data
    spec:
      serviceAccountName: carbon-data-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: carbon-data
          image: ghcr.io/carbonrobotics/cloud/carbon-data:master
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: carbon-data
            - secretRef:
                name: carbon-data
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          resources:
            requests:
              memory: "128Mi"
            limits:
              memory: "512Mi"