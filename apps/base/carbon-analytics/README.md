# Carbon Analytics

Service account `carbon-analytics-sa` required to exist in the same namespace and be bound to
CarbonAnalyticsServicePolicy found in [apps.tf](../../../terraform/aws/apps.tf)

Likely this should be done with eksctl ex:
```
eksctl create iamserviceaccount \
--name carbon-analytics-sa \
--namespace production \
--cluster carbon-cloud \
--attach-policy-arn arn:aws:iam::************:policy/CarbonAnalyticsServicePolicy \
--override-existing-serviceaccounts \
--region us-west-2 \
--approve
```