apiVersion: apps/v1
kind: Deployment
metadata:
  name: carbon-analytics
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: carbon-analytics
  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: carbon-analytics
    spec:
      serviceAccountName: carbon-analytics-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: analytics
          image: ghcr.io/carbonrobotics/cloud/carbon-analytics:unknown
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: carbon-analytics
            - secretRef:
                name: carbon-analytics
          imagePullPolicy: Always
          ports:
            - containerPort: 8501
          resources:
            requests:
              memory: "6Gi"
            limits:
              memory: "18Gi"
              ephemeral-storage: "10Gi"
          volumeMounts:
            - name: ephemeral-data
              mountPath: /data
        - name: oauth2-proxy
          image: quay.io/oauth2-proxy/oauth2-proxy:v7.5.1
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: carbon-analytics
          ports:
            - containerPort: 4180
      volumes:
        - name: ephemeral-data
          ephemeral:
            volumeClaimTemplate:
              metadata:
                labels:
                  app.kubernetes.io/name: carbon-analytics
              spec:
                accessModes: ["ReadWriteOnce"]
                storageClassName: "ebs-sc"
                resources:
                  requests:
                    storage: 500Gi
