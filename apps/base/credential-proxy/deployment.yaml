apiVersion: apps/v1
kind: Deployment
metadata:
  name: credential-proxy
spec:
  strategy:
    type: Recreate
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: credential-proxy
  template:
    metadata:
      labels:
        app.kubernetes.io/name: credential-proxy
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: credential-proxy
          image: ghcr.io/carbonrobotics/cloud/credential-proxy:master
          envFrom:
            - secretRef:
                name: credential-proxy
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          livenessProbe:
            httpGet:
              path: /metrics
              port: 8080
            initialDelaySeconds: 5
            failureThreshold: 1
            periodSeconds: 60
          readinessProbe:
            httpGet:
              path: /metrics
              port: 8080
            initialDelaySeconds: 3
            periodSeconds: 10
          volumeMounts:
            - name: data
              mountPath: /data
              readOnly: false
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: credential-proxy