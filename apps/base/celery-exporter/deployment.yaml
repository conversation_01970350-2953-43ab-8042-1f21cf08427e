apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-exporter
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: celery-exporter
  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: celery-exporter
    spec:
      containers:
        - name: celery-exporter
          image: ghcr.io/danihodovic/celery-exporter
          envFrom:
            - configMapRef:
                name: celery-exporter
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9808
          resources:
            requests:
              memory: "128Mi"
            limits:
              memory: "512Mi"
