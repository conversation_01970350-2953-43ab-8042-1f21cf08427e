apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-verifier
  labels:
    app: api-verifier
spec:
  strategy:
    type: Recreate
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: api-verifier
  template:
    metadata:
      labels:
        app: api-verifier
        name: api-verifier
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: api-verifier
          image: ghcr.io/carbonrobotics/cloud/api-verifier:master
          envFrom:
            - configMapRef:
                name: api-verifier
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          volumeMounts:
            - name: data
              mountPath: /data
              readOnly: false
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: api-verifier