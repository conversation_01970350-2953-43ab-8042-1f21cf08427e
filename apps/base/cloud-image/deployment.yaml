---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-image
  labels:
    app.kubernetes.io/app: cloud-image
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/app: cloud-image
  template:
    metadata:
      labels:
        app.kubernetes.io/app: cloud-image
    spec:
      serviceAccountName: carbon-cloud-image-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: cloud-image
          image: ghcr.io/carbonrobotics/cloud/cloud-image:master
          imagePullPolicy: Always
          ports:
          - containerPort: 8080
          envFrom:
            - configMapRef:
                name: cloud-image
          resources:
            requests:
              memory: "16Mi"
            limits:
              memory: "3Gi"
