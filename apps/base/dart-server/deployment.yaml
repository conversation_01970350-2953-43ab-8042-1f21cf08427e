apiVersion: apps/v1
kind: Deployment
metadata:
  name: dart-server
spec:
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: dart-server
  template:
    metadata:
      labels:
        app.kubernetes.io/name: dart-server
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: dart-server
          image: ghcr.io/carbonrobotics/operator/dart-server:master
          imagePullPolicy: Always
          command:
            - "/app/bin/server"
            - "--host=0.0.0.0"
            - "--port=61002"
          ports:
            - containerPort: 61002
          resources:
            requests:
              memory: "128Mi"
            limits:
              memory: "512Mi"
