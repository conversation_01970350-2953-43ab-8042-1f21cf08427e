apiVersion: apps/v1
kind: Deployment
metadata:
  name: capp
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: capp
  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: capp
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: analytics
          image: ghcr.io/carbonrobotics/capp/capp:master
          env:
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: /credentials/credentials.json
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: capp
            - secretRef:
                name: capp
          imagePullPolicy: Always
          ports:
            - containerPort: 8501
          resources:
            requests:
              memory: "512Mi"
            limits:
              memory: "2Gi"
              ephemeral-storage: "1Gi"
          volumeMounts:
            - name: google-creds
              mountPath: /credentials
              readOnly: true

        - name: oauth2-proxy
          image: quay.io/oauth2-proxy/oauth2-proxy:v7.5.1
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: capp
          ports:
            - containerPort: 4183
      volumes:
        - name: google-creds
          secret:
            secretName: capp
            optional: true
            items:
              - key: credentials.json
                path: credentials.json
