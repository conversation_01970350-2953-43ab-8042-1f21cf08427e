apiVersion: apps/v1
kind: Deployment
metadata:
  name: carbon-bnp
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: carbon-bnp
  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: carbon-bnp
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: bnp
          image: ghcr.io/carbonrobotics/carbon-build_v4/bnp:master
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: carbon-bnp
            - secretRef:
                name: carbon-bnp
                optional: true
          imagePullPolicy: Always
          ports:
            - containerPort: 8501
          resources:
            requests:
              memory: "128Mi"
            limits:
              memory: "256Mi"
              ephemeral-storage: "2Gi"
        - name: oauth2-proxy
          image: quay.io/oauth2-proxy/oauth2-proxy:v7.5.1
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: carbon-bnp
          ports:
            - containerPort: 4180
